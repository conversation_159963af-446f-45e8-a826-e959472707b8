# =====================================================================
# EyeLink 1000  vs  DeepEye-Webcam   ·   Accuracy & Precision Analyse
# =====================================================================
# - Leest eyelink.csv & deepeye.csv (paden hieronder aanpassen indien nodig)
# - Houdt alleen gedeelde deelnemers (participant-ID == RECORDING_SESSION_LABEL)
# - Berekent offset_cm, RMS_cm, SD_cm per fixatie
# - Globale paired-test + <PERSON>’s d
# - RM-ANOVA  (System × Dot)   →  fallback Mixed-Model als data ongebalanceerd is
# - Holm-Bonferroni post-hoc per stip (waar interactie sig. is)
# - Bland-Altman-plots voor Offset & RMS
# =====================================================================

import numpy as np
import pandas as pd
from scipy import stats
import statsmodels.formula.api as smf
from statsmodels.stats.anova import AnovaRM
from statsmodels.stats.multitest import multipletests
import matplotlib.pyplot as plt

# ---------------------------------------------------------------------
# 0.  BESTANDSPADEN
# ---------------------------------------------------------------------
csv_eye = "/Users/<USER>/Documents/Bewegingswetenschappen/BOP/MullerLyer_eyelink/eyelink.csv"
csv_web = "/Users/<USER>/Documents/Bewegingswetenschappen/BOP/MullerLyer_eyelink/deepeye.csv"

# ---------------------------------------------------------------------
# 1.  Loader-functies
# ---------------------------------------------------------------------
def load_eye(path: str) -> pd.DataFrame:
    keep = ["RECORDING_SESSION_LABEL", "unique_dot",
            "user_pred_px_x", "user_pred_px_y",
            "accprecX", "accprecY", "scale_cm_in_px"]
    df = pd.read_csv(path)[keep].copy()
    df.rename(columns={"RECORDING_SESSION_LABEL": "participant"}, inplace=True)
    df["system"]      = "EyeLink"
    df["participant"] = df["participant"].astype(str)
    df["unique_dot"]  = df["unique_dot"].astype(int)
    for c in ["user_pred_px_x","user_pred_px_y",
              "accprecX","accprecY","scale_cm_in_px"]:
        df[c] = df[c].astype(float)
    return df


def load_web(path: str) -> pd.DataFrame:
    keep = ["pp_id", "unique_dot",
            "user_pred_px_x", "user_pred_px_y",
            "accprecX", "accprecY",
            "scale_cm_in_px", "type_dataset"]
    df = pd.read_csv(path)[keep].copy()
    df = df[df["type_dataset"].eq("record")].drop(columns="type_dataset")
    df.rename(columns={"pp_id": "participant"}, inplace=True)
    df["system"]      = "Webcam"
    df["participant"] = df["participant"].astype(str)
    df["unique_dot"]  = df["unique_dot"].astype(int)
    for c in ["user_pred_px_x","user_pred_px_y",
              "accprecX","accprecY","scale_cm_in_px"]:
        df[c] = df[c].astype(float)
    return df

# ---------------------------------------------------------------------
# 2.  Metrics per fixatie
# ---------------------------------------------------------------------
def add_metrics(df: pd.DataFrame) -> pd.DataFrame:
    dx = df["user_pred_px_x"] - df["accprecX"]
    dy = df["user_pred_px_y"] - df["accprecY"]
    df["offset_cm"]    = np.hypot(dx, dy) * df["scale_cm_in_px"]
    df["offset_sq_cm"] = df["offset_cm"] ** 2
    med = (df.groupby(["participant", "unique_dot"])
             [["user_pred_px_x", "user_pred_px_y"]]
             .transform("median"))
    df["sd_cm"] = np.hypot(df["user_pred_px_x"] - med["user_pred_px_x"],
                           df["user_pred_px_y"] - med["user_pred_px_y"]) * df["scale_cm_in_px"]
    return df

# ---------------------------------------------------------------------
# 3.  Laden → deelnemers-kruising → metrics
# ---------------------------------------------------------------------
df_eye = load_eye(csv_eye)
df_web = load_web(csv_web)

common = set(df_eye.participant) & set(df_web.participant)
if not common:
    raise SystemExit("⚠️  Geen gedeelde deelnemers – check ID’s!")

df = pd.concat([df_eye[df_eye.participant.isin(common)],
                df_web[df_web.participant.isin(common)]],
               ignore_index=True)
df = add_metrics(df)
print(f"Analyse op {len(common)} gedeelde deelnemers — {len(df):,} fixaties\n")

# ---------------------------------------------------------------------
# 4.  Globale paired-tests  + Cohen’s d
# ---------------------------------------------------------------------
agg = (df.groupby(["participant", "system"])
         .agg(offset_cm_mean=("offset_cm", "mean"),
              rms_cm        =("offset_sq_cm", lambda x: np.sqrt(x.mean())),
              sd_cm_mean    =("sd_cm", "mean"))
         .reset_index())
wide = agg.pivot(index="participant", columns="system")

def paired_stats(a, b, label):
    diff = a - b
    normal = (len(diff) >= 3) and (stats.shapiro(diff).pvalue > .05)
    if normal:
        t, p = stats.ttest_rel(a, b)
        test = f"paired t   t={t:.2f}"
    else:
        w, p = stats.wilcoxon(a, b, zero_method="zsplit")
        test = f"Wilcoxon    W={w:.0f}"
    d = diff.mean() / diff.std(ddof=1)      # Cohen’s dz
    print(f"{label:<12} {test:<15} p={p:.4f}   d={d:.2f}")

print("— Globale systeemvergelijking (EyeLink − Webcam) —")
paired_stats(wide[("offset_cm_mean","EyeLink")], wide[("offset_cm_mean","Webcam")], "Offset (cm)")
paired_stats(wide[("rms_cm","EyeLink")],        wide[("rms_cm","Webcam")],        "RMS (cm)")
paired_stats(wide[("sd_cm_mean","EyeLink")],    wide[("sd_cm_mean","Webcam")],    "SD (cm)")

# ---------------------------------------------------------------------
# 5.  System × Dot  —  RM-ANOVA → Mixed-Model fallback
# ---------------------------------------------------------------------
df_long = (df.groupby(["participant", "system", "unique_dot"])
             .agg(offset_cm_mean=("offset_cm", "mean"),
                  rms_cm        =("offset_sq_cm", lambda x: np.sqrt(x.mean())),
                  sd_cm_mean    =("sd_cm", "mean"))
             .reset_index())

def rm_or_mixed(dv: str):
    print(f"\n── {dv} ─────────────────────────────────────")
    try:
        aov = AnovaRM(df_long, depvar=dv, subject="participant",
                      within=["system", "unique_dot"]).fit()
        print(aov)
        # partial η² (op basis van F, df’s) voor hoofd­effect systeem
        F   = float(aov.anova_table.loc["system","F Value"])
        df1 = float(aov.anova_table.loc["system","Num DF"])
        df2 = float(aov.anova_table.loc["system","Den DF"])
        etasq = (F * df1) / (F * df1 + df2)
        print(f"Partial η²_systeem = {etasq:.3f}")
    except ValueError:
        print("⚠️  Unbalanced → Mixed Model voor", dv)
        mm = smf.mixedlm(f"{dv} ~ system * C(unique_dot)",
                         df_long, groups="participant").fit()
        print(mm.summary())

for dv in ["offset_cm_mean", "rms_cm", "sd_cm_mean"]:
    rm_or_mixed(dv)

# ---------------------------------------------------------------------
# 6.  Holm-Bonferroni post-hoc per stip
#     (gebruik alleen voor metrics waarvan interactie sig. was)
# ---------------------------------------------------------------------
def holm_posthoc(df_long: pd.DataFrame, dv: str, alpha=.05):
    p_raw, dots = [], []
    for d in sorted(df_long.unique_dot.unique()):
        sub = df_long[df_long.unique_dot == d]
        p   = stats.ttest_rel(sub[sub.system=="EyeLink"][dv],
                              sub[sub.system=="Webcam"][dv]).pvalue
        p_raw.append(p); dots.append(d)
    p_adj = multipletests(p_raw, method="holm")[1]
    print(f"\nPost-hoc Holm voor {dv}")
    for dot, ph in zip(dots, p_adj):
        if ph < alpha:
            print(f"  Dot {dot:2d}: p_Holm={ph:.4f}")

holm_posthoc(df_long, "offset_cm_mean")
holm_posthoc(df_long, "sd_cm_mean")

# ---------------------------------------------------------------------
# 7.  Bland-Altman-plots
# ---------------------------------------------------------------------
def bland_altman(a, b, title):
    mean = (a + b) / 2
    diff = a - b
    md, sd = diff.mean(), diff.std(ddof=1)
    plt.figure(figsize=(5,4))
    plt.scatter(mean, diff, s=10, alpha=.5)
    plt.axhline(md,        ls='--', lw=1)
    plt.axhline(md+1.96*sd, ls='--', lw=1, color='r')
    plt.axhline(md-1.96*sd, ls='--', lw=1, color='r')
    plt.title(f"Bland–Altman {title}")
    plt.xlabel("Gemiddelde (cm)")
    plt.ylabel("Verschil EL − WC (cm)")
    plt.tight_layout()

bland_altman(wide[("offset_cm_mean","EyeLink")],
             wide[("offset_cm_mean","Webcam")], "Offset")
bland_altman(wide[("rms_cm","EyeLink")],
             wide[("rms_cm","Webcam")], "RMS")
plt.show()
