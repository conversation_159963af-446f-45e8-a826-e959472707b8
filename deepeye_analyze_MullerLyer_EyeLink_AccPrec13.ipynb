import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import cm
import matplotlib.patches as patches
import os
import astropy.convolution as krn
import scipy.stats as stats
import seaborn as sns
import statsmodels.api as sm
from statsmodels.stats.anova import AnovaRM

from deepeye_analysis_package.plotting import makeHeat

def compute_rms(samples):
    # Assuming `samples` is a list of tuples, each containing (x, y) coordinates
    deltas = np.diff(samples, axis=0)
    distances = np.linalg.norm(deltas, axis=1)
    rms = np.sqrt(np.mean(distances**2))
    return rms

# Path to data folders
path_to_folders = './CollectedData/complete/data'

# Get all folder names
all_items_in_folders = os.listdir(path_to_folders)
folder_names = []
for item in all_items_in_folders:
    if not item.startswith('.') and os.path.isdir(os.path.join(path_to_folders, item)):
        folder_names.append(item)
        
# folder_names = os.listdir(path_to_folders)

# List to accumulate datasets across subjects
pp_list = []

# Define target resolution, since datasets may have different screen resolutions
TARG_RES = True
if TARG_RES:
    target_resX, target_resY = 1920, 1080
else:
    target_resX, target_resY = None, None

for fn in folder_names:
    path = os.path.join(path_to_folders, fn, f"{fn}_accprec_record.csv")

    # read everything in as strings (so the header‐rows don't blow up type inference)
    df = pd.read_csv(path, sep=',', dtype=str)

    # drop any row where frameNr is literally the string "frameNr"
    df = df[df['frameNr'] != 'frameNr']

    # now you can coerce types if you like
    df['frameNr'] = df['frameNr'].astype(int)
    df['sampTime'] = df['sampTime'].astype(float)
    df['accprecX'] = df['accprecX'].astype(float)
    df['accprecY'] = df['accprecY'].astype(float)
    df['user_pred_px_x'] = df['user_pred_px_x'].astype(float)
    df['user_pred_px_y'] = df['user_pred_px_y'].astype(float)
    df['resX'] = df['resX'].astype(float)
    df['resY'] = df['resY'].astype(float)
    df['scrW_cm'] = df['scrW_cm'].astype(float)        
   
    df = df.drop_duplicates(subset=['user_pred_px_x', 'user_pred_px_y'], ignore_index=True)  # Ensure unique coordinates (sometimes webcam is stuck)
    
    df['scale_cm_in_px'] = df['scrW_cm'] / df['resX']  # Scaling factor for converting px to cm

    # Identify unique dot positions
    _, indices = np.unique(df[['accprecX', 'accprecY']], axis=0, return_inverse=True)
    df['unique_dot'] = indices

    # Identify unique dot positions
    unique_dots = df[['accprecX', 'accprecY']].drop_duplicates()

    # Filter out participants if they have fewer unique dots than the number in 'numCalibDots'
    required_dots = 13
    if len(unique_dots) < required_dots:
        print(f"Skipping participant {fn} with fewer unique dots ({len(unique_dots)}) than required ({required_dots})")
        continue  # Skip this participant if they have fewer unique dots

    # Append the cleaned dataset to the list
    pp_list.append(df)

# Concatenate all subject data into a single DataFrame
all_df = pd.concat(pp_list, ignore_index=True)
all_df.to_csv("deepeye.csv", index=False)

def plot_heatmap(df, target_resX, target_resY):
    # Generate heatmap for all gaze points
    heatmap = makeHeat([target_resX, target_resY], df['user_pred_px_x'].values, df['user_pred_px_y'].values)
    
    # Calculate median gaze predictions and ground truth per dot
    median_pred = df.groupby('unique_dot')[['user_pred_px_x', 'user_pred_px_y']].median()
    median_pred.columns = ['median_pred_x', 'median_pred_y']
    
    true_pos = df.groupby('unique_dot')[['accprecX', 'accprecY']].mean()
    
    # Calculate offset between median gaze predictions and ground truth, then convert to cm
    offset_px = np.linalg.norm(median_pred.values - true_pos.values, axis=1)
    # new scaling factor is used to convert px to cm for scaled data
    offset_cm = offset_px * df['scale_cm_in_px'].iloc[0]
    
    # Calculate Euclidean distance from each gaze point to the median prediction for each dot
    df = df.merge(median_pred, on='unique_dot', how='left')

    df['eucl_dist_gaze_to_median_px'] = np.linalg.norm(
        df[['user_pred_px_x', 'user_pred_px_y']].values - df[['median_pred_x', 'median_pred_y']].values,
        axis=1
    )    
    
    # Calculate the mean Euclidean distance per dot and convert to cm for standard deviation (SD)
    SD_px = df.groupby('unique_dot')['eucl_dist_gaze_to_median_px'].mean()
    # new scaling factor is used to convert px to cm for scaled data
    SD_cm = SD_px * df['scale_cm_in_px'].iloc[0]

    # Plot heatmap and ground truth vs. predicted gaze points
    plt.figure()
    plt.imshow(heatmap, cmap=cm.hot, extent=[0, target_resX, target_resY, 0], alpha=0.5, aspect='equal')
    
    # Plot ground truth positions (green) and median predicted gaze (blue)
    plt.scatter(true_pos['accprecX'], true_pos['accprecY'], c='g', s=40, alpha=0.5, label='Ground Truth')
    plt.scatter(median_pred['median_pred_x'], median_pred['median_pred_y'], c='b', s=40, alpha=0.5, label='Predicted Gaze')
    
    # Draw lines between ground truth and predictions for visualizing offsets
    plt.plot([median_pred['median_pred_x'], true_pos['accprecX']], [median_pred['median_pred_y'], true_pos['accprecY']], c='black')
            
    # Title and statistics in the legend
    plt.title(f'N={df["pp_id"].nunique()}\n Offset: {offset_cm.mean():.1f}cm | SD: {SD_cm.mean():.1f}cm', fontsize=12)
    
    # Annotate offset values near each ground truth dot position
    for (x, y, e) in zip(true_pos['accprecX'], true_pos['accprecY'], offset_cm.round(1)):
        plt.text(x, y, str(e), fontsize=10, color='black')
    
    # Add legend and final touches
    plt.suptitle('Calibration (All Participants, DeepEye)', fontsize=14)
    plt.subplots_adjust(top=0.97)
    plt.legend(loc='upper right', bbox_to_anchor=(1.1, 1.2))
    
    # Show the plot
    plt.savefig('heatmap.png', dpi=300, bbox_inches='tight', facecolor='white')
    plt.show()

# Call the function with the dataframe
plot_heatmap(all_df, target_resX, target_resY)


# Prepare summary data across subjects
summary_data = []

# Calculate metrics for each subject and unique dot
for _, group in all_df.groupby(['pp_id', 'unique_dot']):
    # Compute median gaze for each unique dot
    median_x = group['user_pred_px_x'].median()
    median_y = group['user_pred_px_y'].median()
    
    # Sort by timestamp to ensure temporal order
    group.sort_values('frameNr', inplace=True)    

    # Compute Euclidean distances
    gaze_coords = group[['user_pred_px_x', 'user_pred_px_y']].to_numpy()
    median_coords = np.array([median_x, median_y])
    
    # Distance from gaze samples to median gaze (precision)
    group['eucl_dist_gaze_to_median_px'] = np.linalg.norm(gaze_coords - median_coords, axis=1)
    group['eucl_dist_gaze_to_median_cm'] = group['eucl_dist_gaze_to_median_px'] * all_df['scale_cm_in_px'].iloc[0]
    
    # Offset from median gaze to ground truth (accuracy)
    ground_truth_coords = group[['accprecX', 'accprecY']].to_numpy()
    group['median_offset_px'] = np.linalg.norm(median_coords - ground_truth_coords, axis=1)
    group['median_offset_cm'] = group['median_offset_px'] * all_df['scale_cm_in_px'].iloc[0]

    # RMS error sample-to-sample (temproal precision)
    group['rms_error_px'] = compute_rms(gaze_coords)
    group['rms_error_cm'] = group['rms_error_px'] * all_df['scale_cm_in_px'].iloc[0]
    
    summary_data.append(group)

# Combine data across all subjects and calculate mean precision (SD) and accuracy (offset)
summary_df = pd.concat(summary_data)

def plot_measurement_per_participant(df, measurement='median_offset_cm', name='', color='b'):
    # Compact figure for matching inset plot style
    fig, ax = plt.subplots(figsize=(4, 6))  # Smaller size for side-by-side presentation

    # Calculate measurement per participant and mean value
    measurement_pp = df.groupby('pp_id', as_index=False)[measurement].mean()
    mean_value = measurement_pp[measurement].mean()

    # Simplified box plot to match inset style
    sns.boxplot(y=measurement_pp[measurement], ax=ax, color=color)
    # sns.rugplot(y=measurement_pp[measurement], ax=ax, height=0.05, color=color)
    # ax.axhline(mean_value, color='k', linestyle='--', linewidth=1.5, label=f'Mean: {mean_value:.2f}')
    
    # Set y-axis limits and label
    ax.set_ylim(-0.5, 5)
    ax.set_ylabel(f'{name}', fontsize=16, labelpad=6)
    ax.tick_params(axis='both', labelsize=10)
    
    # Simplify layout: remove x-axis label and set title within the plot
    ax.set_xlabel('')
    ax.set_title(f'Mean: {mean_value:.2f}', fontsize=18, weight='bold', pad=8)
    
    # Compact layout adjustments
    fig.tight_layout()
    plt.savefig(f'{name}_summary.jpg', dpi=1000)
    plt.show()

    # Uncomment the line below to save the plot
    # fig.savefig(f'{name}_summary.jpg', dpi=1000)


summary_df_per_pp= summary_df.groupby(['pp_id'])[['median_offset_cm', 'eucl_dist_gaze_to_median_cm', 'rms_error_cm']].mean().reset_index()

plot_measurement_per_participant(summary_df_per_pp, measurement='median_offset_cm', name='Offset (cm)', color='b')
plot_measurement_per_participant(summary_df_per_pp, measurement='eucl_dist_gaze_to_median_cm', name='SD (cm)', color='orange')
plot_measurement_per_participant(summary_df_per_pp, measurement='rms_error_cm', name='RMS Error (cm)', color='g')

# Participant descriptive statistics (gaze samples per participant)
descr_stats = summary_df.groupby(['pp_id'])['unique_dot'].count().reset_index()
print(f'Descriptive Stats:\n {descr_stats}')

# Participant descriptive statistics (gaze samples per participant)
descr_stats2 = summary_df.groupby(['pp_id','unique_dot'])['median_offset_cm'].count().reset_index()
descr_stats3 = descr_stats2.groupby(['pp_id']).unique_dot.count().reset_index()
print(f'Mean unique dots:\n {descr_stats3}')

def plot_offset_per_dot_with_positions(summary_df, measurement='Offset', screen_width=1920, screen_height=1080):
    """
    Plots offset across participants for each dot with a small illustration under each box plot
    indicating each dot's position on the screen.
    
    Parameters:
    - summary_df (pd.DataFrame): Dataframe containing offset data for each dot and participant.    
    - screen_width (int): Width of the screen (for relative positioning).
    - screen_height (int): Height of the screen (for relative positioning).
    """
    
    # Set up the main figure and the box plot
    plt.figure(figsize=(16, 10))  # Larger figure for readability
    main_ax = plt.gca()
    
    # Increase font sizes for labels, ticks, and title
    main_ax.set_xlabel('Unique Dot', fontsize=18, labelpad=10)
    main_ax.set_ylabel(f'{measurement} (cm)', fontsize=18, labelpad=10)
    main_ax.set_title(f'{measurement} per Dot Across Participants', fontsize=20, weight='bold', pad=15)
    main_ax.tick_params(axis='both', labelsize=12)
    
    # Set y-ticks for clearer reading
    plt.yticks([0, 1, 2, 3, 4, 5, 6, 7, 8, 9])
    
    # Box plot with enhanced appearance
    palette = sns.color_palette("Set2", len(summary_df['unique_dot'].unique()))
    if measurement == 'Offset':
        sns.boxplot(x='unique_dot', y='median_offset_cm', data=summary_df, hue='unique_dot', palette=palette, ax=main_ax, linewidth=1.5)
    elif measurement == 'SD':
        sns.boxplot(x='unique_dot', y='eucl_dist_gaze_to_median_cm', data=summary_df, hue='unique_dot', palette=palette, ax=main_ax, linewidth=1.5)
    elif measurement == 'RMS':
        sns.boxplot(x='unique_dot', y='rms_error_cm', data=summary_df, hue='unique_dot', palette=palette, ax=main_ax, linewidth=1.5)
    
    main_ax.get_legend().remove()  # Remove legend if unnecessary

    # Draw small position illustrations below each unique dot box
    unique_dots = summary_df['unique_dot'].unique()
    for i, dot in enumerate(unique_dots):
        dot_data = summary_df[summary_df['unique_dot'] == dot]
        x_avg = dot_data['accprecX'].mean()
        y_avg = dot_data['accprecY'].mean()
        
        # Define inset axes positions for more space
        inset_ax = main_ax.inset_axes([i / len(unique_dots) + 0.01, -0.25, 0.9 / len(unique_dots), 0.1])
        
        # Draw rectangle and plot dot’s position
        inset_ax.add_patch(patches.Rectangle((0, 0), screen_width, screen_height, edgecolor='gray', facecolor='lightgray', linewidth=0.5))
        inset_ax.plot(x_avg / 1920 * screen_width, y_avg / 1080 * screen_height, 'ro', markersize=6)
        
        # Customize the inset plot appearance
        inset_ax.set_xlim(0, screen_width)
        inset_ax.set_ylim(0, screen_height)
        inset_ax.invert_yaxis()
        inset_ax.axis('off')  # Hide inset plot axes for clarity

    # Layout adjustments for clarity
    plt.tight_layout(pad=3)
    plt.subplots_adjust(top=0.85, bottom=0.25)
    plt.savefig(f'{measurement}_per_dot.jpg', dpi=1000)
    plt.show()

# Example usage:
# plot_offset_per_dot_with_positions(summary_df, measurement='Offset', screen_width=192, screen_height=1080)


summary_df_per_dot= summary_df.groupby(['pp_id','unique_dot', 'accprecX', 'accprecY'])[['median_offset_cm', 'eucl_dist_gaze_to_median_cm', 'rms_error_cm']].mean().reset_index()
max_value = summary_df_per_dot['median_offset_cm'].max()
max_index = summary_df_per_dot['median_offset_cm'].idxmax()
print("Maximum value:", max_value)
print("Index of maximum value:", max_index)
avg_offset_per_dot = (summary_df_per_dot.groupby('unique_dot')['median_offset_cm'].mean().reset_index(name='median_offset_cm'))
print(avg_offset_per_dot)
plot_offset_per_dot_with_positions(summary_df_per_dot, measurement='Offset')
plot_offset_per_dot_with_positions(summary_df_per_dot, measurement='SD')
plot_offset_per_dot_with_positions(summary_df_per_dot, measurement='RMS')

# Definieer de metrieken en hun gewenste prefixen voor de output tabel
metric_name_map = {
    'median_offset_cm': 'Offset',
    'eucl_dist_gaze_to_median_cm': 'Precisie', # Voorheen 'Precision' in mijn gedachten
    'rms_error_cm': 'RMS'
}

# Maak een dictionary voor named aggregations
named_aggregations = {}
for original_col_name, display_prefix in metric_name_map.items():
    named_aggregations[f'{display_prefix}_Mean_cm'] = pd.NamedAgg(column=original_col_name, aggfunc='mean')
    named_aggregations[f'{display_prefix}_Median_cm'] = pd.NamedAgg(column=original_col_name, aggfunc='median')
    named_aggregations[f'{display_prefix}_Q1_cm'] = pd.NamedAgg(column=original_col_name, aggfunc=lambda x: x.quantile(0.25))
    named_aggregations[f'{display_prefix}_Q3_cm'] = pd.NamedAgg(column=original_col_name, aggfunc=lambda x: x.quantile(0.75))

# Groepeer per 'unique_dot' en pas de named aggregations toe
if not summary_df_per_dot.empty: # Controleer of de DataFrame niet leeg is
    stats_per_dot_aggregated = summary_df_per_dot.groupby('unique_dot').agg(**named_aggregations)

    # Bereken de Interkwartielafstand (IQR) voor elke metriek
    for display_prefix in metric_name_map.values():
        q1_col = f'{display_prefix}_Q1_cm'
        q3_col = f'{display_prefix}_Q3_cm'
        if q1_col in stats_per_dot_aggregated.columns and q3_col in stats_per_dot_aggregated.columns:
            stats_per_dot_aggregated[f'{display_prefix}_IQR_cm'] = stats_per_dot_aggregated[q3_col] - stats_per_dot_aggregated[q1_col]

    # Optioneel: Orden de kolommen voor een logische structuur in de tabel
    final_columns_order = []
    for display_prefix in metric_name_map.values():
        final_columns_order.extend([
            f'{display_prefix}_Mean_cm', f'{display_prefix}_Median_cm',
            f'{display_prefix}_Q1_cm', f'{display_prefix}_Q3_cm', f'{display_prefix}_IQR_cm'
        ])
    
    # Filter op kolommen die daadwerkelijk bestaan na aggregatie en IQR berekening
    existing_final_columns = [col for col in final_columns_order if col in stats_per_dot_aggregated.columns]
    stats_per_dot_final = stats_per_dot_aggregated[existing_final_columns]

    # 'stats_per_dot_final' heeft 'unique_dot' als index.
    # Als je 'unique_dot' als een gewone kolom wilt, reset dan de index:
    stats_per_dot_table = stats_per_dot_final.reset_index()

    # Print de resulterende tabel
    print("Tabel met statistieken per kalibratiepunt:")
    print(stats_per_dot_table)

    # Optioneel: sla de tabel op naar een CSV-bestand
    stats_per_dot_table.to_csv('kalibratie_statistieken_per_punt.csv', index=False, float_format='%.3f')
else:
    print("De DataFrame 'summary_df_per_dot' is leeg. Controleer de data.")


# Group dots by the column and row they are in
_, col_indices = np.unique(summary_df_per_dot[['accprecX']], axis=0, return_inverse=True)
summary_df_per_dot['unique_column'] = col_indices
_, row_indices = np.unique(summary_df_per_dot[['accprecY']], axis=0, return_inverse=True)
summary_df_per_dot['unique_row'] = row_indices


def plot_matrix(df, measurement='median_offset_cm', title='Average Median Offset Across Positions'):
    # Calculate the average median offset for each dot across all subjects
    average_per_dot = df.groupby('unique_dot', as_index=False)[measurement].mean()
    # print(f'Average dot: {average_per_dot}')

    # Create a 5x5 matrix with NaN values
    dot_matrix = pd.DataFrame(np.nan, index=range(5), columns=range(5))

    # Fill only 13 cells with values from the dataset
    # Adjust this mapping as per the desired layout of filled cells
    positions_to_fill = [
        (0, 0), (0, 2), (0, 4),
        (1, 1), (1, 3),
        (2, 0), (2, 2), (2, 4),
        (3, 1), (3, 3),
        (4, 0), (4, 2), (4, 4)
    ]

    # Dot position data are numbered in columns, thus flip rows and columns here
    for idx, (row, col) in enumerate(positions_to_fill):
        if idx < len(average_per_dot):
            dot_matrix.iloc[col, row] = average_per_dot[measurement].values[idx]

    # Set up the plot
    plt.figure(figsize=(8, 8))
    heatmap = sns.heatmap(dot_matrix, annot=True, fmt=".2f", cmap="YlGnBu",
                          square=True, annot_kws={"size": 12}, linewidths=0.5,
                          linecolor='gray', cbar_kws={'label': measurement})

    # Customize title and labels for readability
    plt.title(title, fontsize=16, weight='bold', pad=12)
    plt.xlabel('Column', fontsize=14, labelpad=10)
    plt.ylabel('Row', fontsize=14, labelpad=10)

    # Set tick labels for rows and columns for clarity
    heatmap.set_xticklabels(range(1, 6), fontsize=12)
    heatmap.set_yticklabels(range(1, 6), fontsize=12, rotation=0)

    # Customize colorbar label size
    heatmap.collections[0].colorbar.ax.tick_params(labelsize=12)

    # Adjust layout for a clean look
    plt.tight_layout()
    plt.savefig(f'{title}.png', dpi=300, bbox_inches='tight', facecolor='white')
    plt.show()

# Call the updated function
plot_matrix(summary_df_per_dot, measurement='median_offset_cm', title='Offset (cm) Across Positions')
plot_matrix(summary_df_per_dot, measurement='eucl_dist_gaze_to_median_cm', title='SD (cm) Across Positions')
plot_matrix(summary_df_per_dot, measurement='rms_error_cm', title='RMS error (cm) Across Positions')

