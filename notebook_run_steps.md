# Steps Taken to Run `deepeye_analyze_MullerLyerNew_EyeLink.ipynb`

## 1. Initial Setup
- Located the notebook and data directories (`complete/data` and `incomplete`).
- Confirmed the notebook processes data from `complete/data` and not from `incomplete`.

## 2. Attempted to Run the Notebook
- Tried running the notebook using `jupyter nbconvert` to process the updated data.
- Encountered an ImportError related to `numpy` and `pandas` (shadowing/corruption issue).

## 3. Troubleshooting Python Environment
- Checked for local `numpy` or `pandas` directories that could shadow the installed packages (none found).
- Attempted to reinstall `numpy` and `pandas` using `pip uninstall -y numpy pandas && pip install numpy pandas`.
- Noted a dependency conflict with `mediapipe` (requires numpy <2), but continued as it is not critical for the notebook.

## 4. Resolved Missing Dependencies
- Encountered a `ModuleNotFoundError` for `astropy`.
- Installed `astropy` using `pip install astropy`.

## 5. Data Formatting Error
- After resolving package issues, the notebook failed due to a `ParserError`:
  - `Error tokenizing data. C error: Expected 23 fields in line 12, saw 30`
- This indicates a malformed CSV file in `complete/data` (inconsistent number of columns).

## 6. Local Installation of Requirements
- Attempted to install all requirements locally with `pip install --user -r requirements.txt`.
- Installation succeeded for all packages except `pywin32` (not available on macOS, not needed).

## 7. Next Steps
- The environment and dependencies are now set up correctly.
- The only remaining blocker is the malformed CSV file(s) in `complete/data`.
- To proceed, inspect and fix the CSV file(s) so all rows have the same number of columns as the header.

---

**If you need help identifying or fixing the problematic CSV file, let me know!** 