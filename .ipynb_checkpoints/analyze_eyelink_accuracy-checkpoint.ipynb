{"cells": [{"cell_type": "markdown", "id": "4f34f26b", "metadata": {}, "source": ["# EyeLink Accuracy/Precision Analysis\n", "\n", "Dit notebook analyseert de accuracy en precisie data verkregen van een EyeLink 1000 eyetracker, gebaseerd op een fixatierapport (`.xls` bestand). Het berekent mediaan offset, standaarddeviatie van gaze (als maat voor precisie), en RMS error, en visualiseert deze in heatmaps."]}, {"cell_type": "markdown", "id": "04c8fa20", "metadata": {}, "source": ["## 1. Import Libraries"]}, {"cell_type": "code", "execution_count": 1, "id": "bd1db2ed", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import openpyxl # Nodig voor pd.read_excel om .xls bestanden te lezen"]}, {"cell_type": "markdown", "id": "438f8315", "metadata": {}, "source": ["## 2. Helper Functions\n", "Definities van functies voor pixel-naar-cm conversie, R<PERSON> berekening, statistieken per doelpunt, en het plotten van de heatmatrix."]}, {"cell_type": "code", "execution_count": 2, "id": "0ab5df61", "metadata": {}, "outputs": [], "source": ["def pixels_to_cm(pixels, screen_dimension_pixels, physical_screen_dimension_cm):\n", "    \"\"\"Converts pixel values to centimeters.\"\"\"\n", "    return (pixels / screen_dimension_pixels) * physical_screen_dimension_cm"]}, {"cell_type": "code", "execution_count": 3, "id": "1cbc469e", "metadata": {}, "outputs": [], "source": ["def compute_rms(samples_cm):\n", "    \"\"\"Computes Root Mean Square of sequential differences in samples.\"\"\"\n", "    if not isinstance(samples_cm, np.ndarray):\n", "        samples_cm = np.array(samples_cm)\n", "    \n", "    if samples_cm.ndim == 1: # Handle case where samples might be passed incorrectly\n", "        if len(samples_cm) < 2 : # if it's a flat list of x,y,x,y...\n", "             return np.nan\n", "        # try to reshape if it's like [x1,y1,x2,y2,...]\n", "        if len(samples_cm) % 2 == 0:\n", "            samples_cm = samples_cm.reshape(-1,2)\n", "        else: # cannot form pairs\n", "            return np.nan\n", "\n", "    if samples_cm.shape[0] < 2: # Need at least two points to compute differences\n", "        return np.nan\n", "        \n", "    deltas = np.diff(samples_cm, axis=0)\n", "    if deltas.shape[0] == 0: # No differences computed\n", "        return np.nan\n", "        \n", "    distances_sq = np.sum(deltas**2, axis=1)\n", "    rms = np.sqrt(np.mean(distances_sq))\n", "    return rms"]}, {"cell_type": "code", "execution_count": 4, "id": "5853785a", "metadata": {}, "outputs": [], "source": ["def calculate_summary_stats(dot_id, target_coords_cm, gaze_points_for_dot_cm):\n", "    \"\"\"\n", "    Calculates median offset, precision (mean Euclidean distance to median gaze),\n", "    and RMS error for a given target dot.\n", "    \n", "    Args:\n", "        dot_id (int/str): Identifier for the dot.\n", "        target_coords_cm (tuple): (x, y) coordinates of the target in cm.\n", "        gaze_points_for_dot_cm (list of tuples): List of (x, y) gaze coordinates in cm for the dot.\n", "    \"\"\"\n", "    if not gaze_points_for_dot_cm:\n", "        return {\n", "            'dot_id': dot_id,\n", "            'median_offset_cm': np.nan,\n", "            'eucl_dist_gaze_to_median_cm': np.nan,\n", "            'rms_error_cm': np.nan,\n", "            'num_fixations': 0\n", "        }\n", "\n", "    gaze_array_cm = np.array(gaze_points_for_dot_cm)\n", "    \n", "    # Median gaze position\n", "    median_gaze_x_cm = np.median(gaze_array_cm[:, 0])\n", "    median_gaze_y_cm = np.median(gaze_array_cm[:, 1])\n", "    median_gaze_coords_cm = (median_gaze_x_cm, median_gaze_y_cm)\n", "\n", "    # Offset: Euclidean distance between median gaze and target\n", "    offset_cm = np.linalg.norm(np.array(median_gaze_coords_cm) - np.array(target_coords_cm))\n", "\n", "    # Precision: Mean Euclidean distance of gaze points from their collective median\n", "    distances_to_median_cm = [np.linalg.norm(point - median_gaze_coords_cm) for point in gaze_array_cm]\n", "    precision_metric_cm = np.mean(distances_to_median_cm)\n", "    \n", "    # RMS error\n", "    rms_cm = compute_rms(gaze_array_cm)\n", "\n", "    return {\n", "        'dot_id': dot_id,\n", "        'median_offset_cm': offset_cm,\n", "        'eucl_dist_gaze_to_median_cm': precision_metric_cm,\n", "        'rms_error_cm': rms_cm,\n", "        'num_fixations': len(gaze_points_for_dot_cm)\n", "    }"]}, {"cell_type": "code", "execution_count": 5, "id": "128554b1", "metadata": {}, "outputs": [], "source": ["def plot_matrix(df_summary, measurement, title):\n", "    \"\"\"Plots a 5x5 heatmap for the given measurement.\"\"\"\n", "    dot_matrix_data = np.full((5, 5), np.nan)\n", "    \n", "    # Ensure dot_ids 0-24 are used for consistent plotting\n", "    # Dataframe might not have all dots if data is missing\n", "    for i in range(5): # Rows\n", "        for j in range(5): # Columns\n", "            dot_index = i * 5 + j\n", "            # Check if this dot_id exists in the summary dataframe\n", "            if dot_index in df_summary['dot_id'].values:\n", "                value = df_summary.loc[df_summary['dot_id'] == dot_index, measurement].iloc[0]\n", "                dot_matrix_data[i, j] = value\n", "            # else it remains NaN, which is fine for heatmap\n", "\n", "    plt.figure(figsize=(10, 8))\n", "    sns.heatmap(dot_matrix_data, annot=True, fmt=\".2f\", cmap=\"viridis\",\n", "                square=True, annot_kws={\"size\": 10}, linewidths=.5,\n", "                cbar_kws={'label': measurement})\n", "    plt.title(title, fontsize=16)\n", "    plt.xlabel(\"Grid Column Index\", fontsize=12)\n", "    plt.ylabel(\"Grid Row Index\", fontsize=12)\n", "    plt.xticks(np.arange(5) + 0.5, labels=np.arange(1, 6)) # Column labels 1-5\n", "    plt.yticks(np.arange(5) + 0.5, labels=np.arange(1, 6), rotation=0) # Row labels 1-5\n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "markdown", "id": "76f18ce6", "metadata": {}, "source": ["## 3. Main Analysis Script"]}, {"cell_type": "markdown", "id": "15550e69", "metadata": {}, "source": ["### 3.1. Define Parameters and File Path"]}, {"cell_type": "code", "execution_count": null, "id": "21a36c6a", "metadata": {}, "outputs": [], "source": ["# Screen Parameters (ASSUMING SAME PHYSICAL SCREEN AS WEBCAM STUDY)\n", "PHYSICAL_SCREEN_WIDTH_CM = 34.4\n", "PHYSICAL_SCREEN_HEIGHT_CM = 19.3\n", "EYELINK_SCREEN_WIDTH_PX = 1920\n", "EYELINK_SCREEN_HEIGHT_PX = 1080\n", "# Kijkafstand = 60 cm (not directly used in these specific cm conversions, but good for context)\n", "\n", "print(f\"Assuming EyeLink physical screen width: {PHYSICAL_SCREEN_WIDTH_CM} cm\")\n", "print(f\"Assuming EyeLink physical screen height: {PHYSICAL_SCREEN_HEIGHT_CM} cm\")\n", "print(f\"EyeLink screen resolution: {EYELINK_SCREEN_WIDTH_PX}x{EYELINK_SCREEN_HEIGHT_PX} px\")\n", "\n", "# File Path (pas dit aan indien nodig)\n", "path_to_folders = './CollectedData/complete/eyelink_data'"]}, {"cell_type": "markdown", "id": "5502f759", "metadata": {}, "source": ["### 3.2. <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "id": "1e5d5b26", "metadata": {}, "outputs": [], "source": ["try:\n", "    # Specify engine='openpyxl' if pandas has trouble with .xls\n", "    df_eyelink_raw = pd.read_excel(file_path) # Add engine='openpyxl' if needed\n", "    print(f\"Successfully loaded data from {file_path}. Shape: {df_eyelink_raw.shape}\")\n", "    # Toon de eerste paar rijen om de kolomnamen te verifiëren\n", "    print(\"First 5 rows of the loaded data:\")\n", "    print(df_eyelink_raw.head())\n", "    print(\"\\nColumn names:\")\n", "    print(df_eyelink_raw.columns.tolist())\n", "except FileNotFoundError:\n", "    print(f\"Error: File not found at {file_path}\")\n", "    df_eyelink_raw = None # Ensure it's defined for later checks\n", "except Exception as e:\n", "    print(f\"An error occurred while loading the Excel file: {e}\")\n", "    df_eyelink_raw = None"]}, {"cell_type": "markdown", "id": "bcc4e129", "metadata": {}, "source": ["### 3.3. <PERSON>lter Data for Accuracy/Precision Task"]}, {"cell_type": "code", "execution_count": null, "id": "30135531", "metadata": {}, "outputs": [], "source": ["df_accprec = None # Initialize\n", "if df_eyelink_raw is not None:\n", "    # Ensure 'arrowLength' column exists\n", "    if 'arrowLength' not in df_eyelink_raw.columns:\n", "        print(\"Error: Column 'arrowLength' not found in the Excel file.\")\n", "        print(\"Please verify column names from the output above.\")\n", "    else:\n", "        # Convert 'arrowLength' to string to handle potential mixed types or numbers read as float\n", "        df_eyelink_raw['arrowLength'] = df_eyelink_raw['arrowLength'].astype(str)\n", "        # Filter rows where 'arrowLength' is \"UNDEFINED\"\n", "        # Also handle cases like \"UNDEFINED.0\" if it was read as float then string\n", "        df_accprec = df_eyelink_raw[df_eyelink_raw['arrowLength'].str.startswith(\"UNDEFINED\")].copy()\n", "\n", "        if df_accprec.empty:\n", "            print(\"No data found for accuracy/precision task (where 'arrowLength' starts with 'UNDEFINED').\")\n", "        else:\n", "            print(f\"Filtered for accuracy/precision task. Shape: {df_accprec.shape}\")\n", "\n", "            # Select and ensure necessary columns are present and not NaN\n", "            required_cols = ['CURRENT_FIX_X', 'CURRENT_FIX_Y', 'accprecX', 'accprecY']\n", "            missing_cols = [col for col in required_cols if col not in df_accprec.columns]\n", "            if missing_cols:\n", "                print(f\"Error: Required columns not found in the filtered data: {missing_cols}\")\n", "                df_accprec = pd.DataFrame() # Empty df if critical columns are missing\n", "            else:\n", "                df_accprec.dropna(subset=required_cols, inplace=True)\n", "                if df_accprec.empty:\n", "                    print(f\"No valid data after dropping NaNs from required columns: {required_cols}\")\n", "                else:\n", "                    print(f\"Data shape after dropping NaNs in critical columns: {df_accprec.shape}\")\n", "else:\n", "    print(\"Skipping filtering as data loading failed.\")"]}, {"cell_type": "markdown", "id": "a9a63372", "metadata": {}, "source": ["### 3.4. Convert Pixel Coordinates to CM"]}, {"cell_type": "code", "execution_count": null, "id": "93f2b988", "metadata": {}, "outputs": [], "source": ["if df_accprec is not None and not df_accprec.empty:\n", "    df_accprec['gaze_x_cm'] = pixels_to_cm(df_accprec['CURRENT_FIX_X'], EYELINK_SCREEN_WIDTH_PX, PHYSICAL_SCREEN_WIDTH_CM)\n", "    df_accprec['gaze_y_cm'] = pixels_to_cm(df_accprec['CURRENT_FIX_Y'], EYELINK_SCREEN_HEIGHT_PX, PHYSICAL_SCREEN_HEIGHT_CM)\n", "    df_accprec['target_x_cm'] = pixels_to_cm(df_accprec['accprecX'], EYELINK_SCREEN_WIDTH_PX, PHYSICAL_SCREEN_WIDTH_CM)\n", "    df_accprec['target_y_cm'] = pixels_to_cm(df_accprec['accprecY'], EYELINK_SCREEN_HEIGHT_PX, PHYSICAL_SCREEN_HEIGHT_CM)\n", "    print(\"Converted pixel coordinates to cm.\")\n", "    # print(df_accprec[['CURRENT_FIX_X', 'gaze_x_cm', 'accprecX', 'target_x_cm']].head())\n", "else:\n", "    print(\"Skipping coordinate conversion as no valid data is available.\")"]}, {"cell_type": "markdown", "id": "20b551d5", "metadata": {}, "source": ["### 3.5. Process Data per Target Dot"]}, {"cell_type": "code", "execution_count": null, "id": "d5b927d7", "metadata": {}, "outputs": [], "source": ["summary_df_per_dot = pd.DataFrame() # Initialize\n", "if df_accprec is not None and not df_accprec.empty:\n", "    # Round target_cm coordinates slightly to group them reliably\n", "    df_accprec['target_x_cm_rounded'] = df_accprec['target_x_cm'].round(3)\n", "    df_accprec['target_y_cm_rounded'] = df_accprec['target_y_cm'].round(3)\n", "\n", "    unique_targets_cm = df_accprec[['target_x_cm_rounded', 'target_y_cm_rounded', 'target_x_cm', 'target_y_cm']]\n", "    unique_targets_cm = unique_targets_cm.drop_duplicates(subset=['target_x_cm_rounded', 'target_y_cm_rounded'])\n", "    # Sort to try and get a consistent dot_id order (top-to-bottom, then left-to-right)\n", "    unique_targets_cm = unique_targets_cm.sort_values(by=['target_y_cm_rounded', 'target_x_cm_rounded']).reset_index(drop=True)\n", "    unique_targets_cm['dot_id'] = unique_targets_cm.index \n", "    \n", "    print(f\"Found {len(unique_targets_cm)} unique target positions.\")\n", "    if len(unique_targets_cm) > 0:\n", "        print(unique_targets_cm[['dot_id', 'target_x_cm', 'target_y_cm']])\n", "    \n", "    if len(unique_targets_cm) != 25 and len(unique_targets_cm) > 0:\n", "        print(f\"Warning: Expected 25 unique target dots for a 5x5 grid, but found {len(unique_targets_cm)}.\")\n", "    elif len(unique_targets_cm) == 0:\n", "        print(\"Error: No unique target positions found. Check 'accprecX' and 'accprecY' data.\")\n", "    \n", "    if not unique_targets_cm.empty:\n", "        # Merge dot_id back into the main acc/prec dataframe\n", "        # Need to use the original (non-rounded for merging key if needed) or ensure rounded is the key\n", "        df_accprec = pd.merge(df_accprec, unique_targets_cm[['target_x_cm_rounded', 'target_y_cm_rounded', 'dot_id']], \n", "                              on=['target_x_cm_rounded', 'target_y_cm_rounded'], how='left')\n", "\n", "        # Calculate stats for each dot\n", "        all_dots_summary_list = []\n", "        for dot_id_val, group in df_accprec.groupby('dot_id'):\n", "            # Get the actual target coordinates for this dot_id from unique_targets_cm for precision\n", "            target_info = unique_targets_cm[unique_targets_cm['dot_id'] == dot_id_val]\n", "            target_coords_cm_actual = (target_info['target_x_cm'].iloc[0], target_info['target_y_cm'].iloc[0])\n", "            \n", "            gaze_points_for_dot_cm = list(zip(group['gaze_x_cm'], group['gaze_y_cm']))\n", "            \n", "            summary = calculate_summary_stats(dot_id_val, target_coords_cm_actual, gaze_points_for_dot_cm)\n", "            all_dots_summary_list.append(summary)\n", "\n", "        summary_df_per_dot = pd.DataFrame(all_dots_summary_list)\n", "\n", "        if summary_df_per_dot.empty:\n", "            print(\"Error: Could not compute summary statistics for any dot.\")\n", "        else:\n", "            print(\"\\nSummary statistics per dot:\")\n", "            print(summary_df_per_dot)\n", "            print(f\"\\nNumber of fixations per dot (min, mean, max): {summary_df_per_dot['num_fixations'].min()}, {summary_df_per_dot['num_fixations'].mean():.2f}, {summary_df_per_dot['num_fixations'].max()}\")\n", "else:\n", "    print(\"Skipping statistics calculation as no valid data is available.\")"]}, {"cell_type": "markdown", "id": "be8cd22b", "metadata": {}, "source": ["### 3.6. Generate Heatmaps"]}, {"cell_type": "code", "execution_count": null, "id": "2ef1af15", "metadata": {}, "outputs": [], "source": ["if not summary_df_per_dot.empty:\n", "    # Pad with NaNs if fewer than 25 dots were found, to make a 5x5 grid for plotting\n", "    # This ensures plot_matrix function receives data that can be shaped or indexed up to 24.\n", "    if len(summary_df_per_dot) < 25:\n", "        print(f\"\\nPadding summary data for plotting as only {len(summary_df_per_dot)} dots had data.\")\n", "        all_possible_dot_ids = pd.DataFrame({'dot_id': range(25)})\n", "        summary_df_per_dot_plot = pd.merge(all_possible_dot_ids, summary_df_per_dot, on='dot_id', how='left')\n", "    else:\n", "        summary_df_per_dot_plot = summary_df_per_dot.copy()\n", "    \n", "    # Ensure dot_id is sorted for plot_matrix logic if it relies on implicit order\n", "    summary_df_per_dot_plot = summary_df_per_dot_plot.sort_values(by='dot_id').reset_index(drop=True)\n", "\n", "    plot_matrix(summary_df_per_dot_plot, measurement='median_offset_cm', title='EyeLink: Offset (cm) Across Positions')\n", "    plot_matrix(summary_df_per_dot_plot, measurement='eucl_dist_gaze_to_median_cm', title='EyeLink: SD (cm) Across Positions')\n", "    plot_matrix(summary_df_per_dot_plot, measurement='rms_error_cm', title='EyeLink: RMS error (cm) Across Positions')\n", "    print(\"\\nSuccessfully generated heatmaps (if any data was processed).\")\n", "else:\n", "    print(\"No summary data to plot.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}